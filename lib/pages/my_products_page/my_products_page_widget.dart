import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:soho_souk/pages/app_button/app_button_widget.dart';
import 'package:soho_souk/pages/profile_page/profile_page_model.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/app_bar/app_bar_widget.dart';
import '/pages/my_products_empty_component/my_products_empty_component_widget.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'my_products_page_model.dart';
export 'my_products_page_model.dart';

class MyProductsPageWidget extends StatefulWidget {
  const MyProductsPageWidget({super.key});

  @override
  State<MyProductsPageWidget> createState() => _MyProductsPageWidgetState();
}

class _MyProductsPageWidgetState extends State<MyProductsPageWidget> {
  late MyProductsPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => MyProductsPageModel());
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  bool loader = true;

  getLatestPost() async {
    // Implementation for getting latest posts
  }

  void deletePost(post_id) async {
    showLoadingDialog(context);
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}delete-postad/${post_id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      Navigator.of(context, rootNavigator: true).pop(false);
      if (response.statusCode == 200) {
        appStore.deletePost(post_id);
        toasty(context, "Post Deleted",
            bgColor: Colors.green, textColor: Colors.black);
        setState(() {});
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      setState(() {
        loader = false;
      });
      print('Error Occurred product' + e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: 'My Post ads',
                ),
              ),
              appStore.userPosts.length > 0
                  ? Expanded(
                      child: Stack(
                        alignment: AlignmentDirectional(0.0, 1.0),
                        children: [
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            child: GridView.builder(
                              padding: EdgeInsets.fromLTRB(0, 20.0, 0, 80.0),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: () {
                                  if (MediaQuery.sizeOf(context).width <
                                      kBreakpointSmall) {
                                    return 2;
                                  } else if (MediaQuery.sizeOf(context).width <
                                      kBreakpointMedium) {
                                    return 3;
                                  } else if (MediaQuery.sizeOf(context).width <
                                      kBreakpointLarge) {
                                    return 4;
                                  } else {
                                    return 5;
                                  }
                                }(),
                                crossAxisSpacing: 12.0,
                                mainAxisSpacing: 16.0,
                                childAspectRatio: 0.75,
                              ),
                              scrollDirection: Axis.vertical,
                              itemCount: appStore.userPosts.length,
                              itemBuilder: (context, discountItemsListIndex) {
                                final recentPost =
                                    appStore.userPosts[discountItemsListIndex];
                                return GestureDetector(
                                  onTap: () {
                                    context.pushNamed(
                                      'ProductDetailPage',
                                      queryParameters: {
                                        'post': jsonEncode(recentPost.toJson()),
                                      }.withoutNulls,
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .secondaryBackground,
                                      boxShadow: [
                                        BoxShadow(
                                          blurRadius: 8.0,
                                          color: Color(0x15000000),
                                          offset: Offset(0.0, 2.0),
                                          spreadRadius: 0.0,
                                        )
                                      ],
                                      borderRadius: BorderRadius.circular(16.0),
                                      border: Border.all(
                                        color: Color(0x08000000),
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          12.0, 12.0, 12.0, 12.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            flex: 3,
                                            child: Stack(
                                              alignment: AlignmentDirectional(
                                                  1.0, -1.0),
                                              children: [
                                                ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          12.0),
                                                  child: CachedNetworkImage(
                                                    fadeInDuration: Duration(
                                                        milliseconds: 500),
                                                    fadeOutDuration: Duration(
                                                        milliseconds: 500),
                                                    imageUrl:
                                                        "${ApiUtils.post_image}${recentPost.image}",
                                                    width: double.infinity,
                                                    height: double.infinity,
                                                    fit: BoxFit.cover,
                                                    placeholder:
                                                        (context, url) =>
                                                            Container(
                                                      color: Color(0xFFF5F5F5),
                                                      child: Center(
                                                        child:
                                                            CircularProgressIndicator(
                                                          strokeWidth: 2.0,
                                                          valueColor:
                                                              AlwaysStoppedAnimation<
                                                                  Color>(
                                                            ClassifiedAppTheme
                                                                    .of(context)
                                                                .primary,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    errorWidget:
                                                        (context, url, error) =>
                                                            Container(
                                                      color: Color(0xFFF5F5F5),
                                                      child: Icon(
                                                        Icons
                                                            .image_not_supported,
                                                        color:
                                                            Color(0xFF9E9E9E),
                                                        size: 32.0,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                // Action buttons overlay
                                                Positioned(
                                                  top: 8.0,
                                                  right: 8.0,
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      // Edit button
                                                      GestureDetector(
                                                        onTap: () {
                                                          context.pushNamed(
                                                            'EditPost',
                                                            queryParameters: {
                                                              'id':
                                                                  serializeParam(
                                                                recentPost.id,
                                                                ParamType.int,
                                                              ),
                                                            }.withoutNulls,
                                                            extra: <String,
                                                                dynamic>{
                                                              kTransitionInfoKey:
                                                                  TransitionInfo(
                                                                hasTransition:
                                                                    true,
                                                                transitionType:
                                                                    PageTransitionType
                                                                        .rightToLeft,
                                                                duration: Duration(
                                                                    milliseconds:
                                                                        300),
                                                              ),
                                                            },
                                                          );
                                                        },
                                                        child: Container(
                                                          width: 32.0,
                                                          height: 32.0,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors.white,
                                                            shape:
                                                                BoxShape.circle,
                                                            boxShadow: [
                                                              BoxShadow(
                                                                blurRadius: 4.0,
                                                                color: Color(
                                                                    0x20000000),
                                                                offset: Offset(
                                                                    0.0, 2.0),
                                                              )
                                                            ],
                                                          ),
                                                          child: Icon(
                                                            Icons.edit,
                                                            size: 16.0,
                                                            color: ClassifiedAppTheme
                                                                    .of(context)
                                                                .primary,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 8.0),
                                                      // Delete button
                                                      GestureDetector(
                                                        onTap: () => showDialog(
                                                          context: context,
                                                          builder:
                                                              (dialogContext) {
                                                            return Dialog(
                                                              elevation: 0,
                                                              insetPadding:
                                                                  EdgeInsets
                                                                      .zero,
                                                              backgroundColor:
                                                                  Colors
                                                                      .transparent,
                                                              alignment: AlignmentDirectional(
                                                                      0.0, 0.0)
                                                                  .resolve(
                                                                      Directionality.of(
                                                                          context)),
                                                              child:
                                                                  GestureDetector(
                                                                onTap: () => _model
                                                                        .unfocusNode
                                                                        .canRequestFocus
                                                                    ? FocusScope.of(
                                                                            context)
                                                                        .requestFocus(_model
                                                                            .unfocusNode)
                                                                    : FocusScope.of(
                                                                            context)
                                                                        .unfocus(),
                                                                child: deletePostWidget(appStore
                                                                        .userPosts[
                                                                            discountItemsListIndex]
                                                                        .id ??
                                                                    0),
                                                              ),
                                                            );
                                                          },
                                                        ).then((value) =>
                                                            setState(() {})),
                                                        child: Container(
                                                          width: 32.0,
                                                          height: 32.0,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors.white,
                                                            shape:
                                                                BoxShape.circle,
                                                            boxShadow: [
                                                              BoxShadow(
                                                                blurRadius: 4.0,
                                                                color: Color(
                                                                    0x20000000),
                                                                offset: Offset(
                                                                    0.0, 2.0),
                                                              )
                                                            ],
                                                          ),
                                                          child: Icon(
                                                            Icons.delete,
                                                            size: 16.0,
                                                            color: Colors.red,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          // Content section
                                          Expanded(
                                            flex: 2,
                                            child: Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 12.0, 0.0, 0.0),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.max,
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // Product name
                                                  AutoSizeText(
                                                    recentPost.post_name
                                                        .toString(),
                                                    maxLines: 2,
                                                    style: ClassifiedAppTheme
                                                            .of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily: 'Satoshi',
                                                          fontSize: 14.0,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          useGoogleFonts: false,
                                                        ),
                                                    minFontSize: 12.0,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                  SizedBox(height: 4.0),
                                                  // Price
                                                  Text(
                                                    "AED ${recentPost.price}",
                                                    style: ClassifiedAppTheme
                                                            .of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily: 'Satoshi',
                                                          fontSize: 16.0,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color:
                                                              ClassifiedAppTheme
                                                                      .of(context)
                                                                  .primary,
                                                          useGoogleFonts: false,
                                                        ),
                                                  ),
                                                  SizedBox(height: 8.0),
                                                  // Vendor info
                                                  Row(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(16.0),
                                                        child: recentPost
                                                                    .vendor_image ==
                                                                ''
                                                            ? Image.asset(
                                                                'assets/images/soho_icon.jpg',
                                                                width: 24.0,
                                                                height: 24.0,
                                                                fit: BoxFit
                                                                    .cover,
                                                              )
                                                            : CachedNetworkImage(
                                                                width: 24.0,
                                                                height: 24.0,
                                                                fit: BoxFit
                                                                    .cover,
                                                                imageUrl:
                                                                    "${ApiUtils.profile_files}${recentPost.vendor_image}",
                                                                placeholder:
                                                                    (context,
                                                                            url) =>
                                                                        Container(
                                                                  width: 24.0,
                                                                  height: 24.0,
                                                                  color: Color(
                                                                      0xFFF5F5F5),
                                                                  child: Center(
                                                                    child:
                                                                        CircularProgressIndicator(
                                                                      strokeWidth:
                                                                          1.5,
                                                                    ),
                                                                  ),
                                                                ),
                                                                errorWidget: (context,
                                                                        url,
                                                                        error) =>
                                                                    Icon(
                                                                        Icons
                                                                            .person,
                                                                        size:
                                                                            24.0),
                                                              ),
                                                      ),
                                                      SizedBox(width: 8.0),
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              recentPost
                                                                  .vendor_name
                                                                  .toString(),
                                                              style: ClassifiedAppTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'Satoshi',
                                                                    fontSize:
                                                                        12.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                            ),
                                                            SizedBox(
                                                                height: 2.0),
                                                            Row(
                                                              children: [
                                                                Icon(
                                                                  Icons
                                                                      .location_on,
                                                                  size: 12.0,
                                                                  color: Color(
                                                                      0xFF9E9E9E),
                                                                ),
                                                                SizedBox(
                                                                    width: 2.0),
                                                                Expanded(
                                                                  child: Text(
                                                                    recentPost
                                                                        .city
                                                                        .toString(),
                                                                    style: ClassifiedAppTheme.of(
                                                                            context)
                                                                        .bodyMedium
                                                                        .override(
                                                                          fontFamily:
                                                                              'Satoshi',
                                                                          fontSize:
                                                                              11.0,
                                                                          fontWeight:
                                                                              FontWeight.w400,
                                                                          color:
                                                                              Color(0xFF9E9E9E),
                                                                          useGoogleFonts:
                                                                              false,
                                                                        ),
                                                                    maxLines: 1,
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/myProductsEmpty.png',
                          width: 120.0,
                          height: 120.0,
                          fit: BoxFit.contain,
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 28.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'r8fti1o5' /* No products yet */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 24.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'tvaeeupe' /* Your products list is empty pl... */,
                            ),
                            textAlign: TextAlign.center,
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              54.0, 28.0, 54.0, 0.0),
                          child: wrapWithModel(
                            model: _model.appBarModel,
                            updateCallback: () => setState(() {}),
                            child: AppButtonWidget(
                              text: 'Go to home',
                              action: () async {},
                            ),
                          ),
                        ),
                      ],
                    )
            ],
          ),
        ),
      ),
    );
  }

  Widget deletePostWidget(int post_id) {
    return Container(
      width: 300.0,
      decoration: BoxDecoration(
        color: ClassifiedAppTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(24.0, 24.0, 24.0, 24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Delete Post",
              style: ClassifiedAppTheme.of(context).bodyMedium.override(
                    fontFamily: 'Satoshi',
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                    useGoogleFonts: false,
                  ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
              child: Text(
                "Are you sure you want to delete this post? This action cannot be undone.",
                textAlign: TextAlign.center,
                style: ClassifiedAppTheme.of(context).bodyMedium.override(
                      fontFamily: 'Satoshi',
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                      useGoogleFonts: false,
                    ),
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 24.0, 0.0, 0.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        context.safePop();
                      },
                      child: Container(
                        height: 48.0,
                        decoration: BoxDecoration(
                          color: Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Text(
                          "Cancel",
                          style: ClassifiedAppTheme.of(context)
                              .bodyMedium
                              .override(
                                fontFamily: 'Satoshi',
                                fontSize: 16.0,
                                fontWeight: FontWeight.w600,
                                useGoogleFonts: false,
                              ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.0),
                  Expanded(
                    child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        deletePost(post_id);
                        context.safePop();
                      },
                      child: Container(
                        height: 48.0,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Text(
                          "Delete",
                          style: ClassifiedAppTheme.of(context)
                              .bodyMedium
                              .override(
                                fontFamily: 'Satoshi',
                                color: Colors.white,
                                fontSize: 16.0,
                                fontWeight: FontWeight.w600,
                                useGoogleFonts: false,
                              ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
